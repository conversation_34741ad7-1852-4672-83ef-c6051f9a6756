{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .payment-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .payment-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .payment-method-icon {
        font-size: 1.2rem;
        margin-right: 0.5rem;
    }
    
    .amount-display {
        font-size: 2rem;
        font-weight: bold;
        text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }
    
    .prescription-info {
        background: #f8f9fc;
        border-left: 4px solid #4e73df;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .btn-pay {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        border: none;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .btn-pay:hover {
        background: linear-gradient(135deg, #13855c 0%, #1cc88a 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-credit-card text-primary"></i>
            {{ title }}
        </h1>
        <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Prescription
        </a>
    </div>

    <div class="row">
        <!-- Payment Summary -->
        <div class="col-lg-4">
            <div class="payment-summary">
                <h5 class="mb-3">
                    <i class="fas fa-file-invoice-dollar"></i>
                    Payment Summary
                </h5>
                <div class="mb-3">
                    <div class="amount-display">₦{{ remaining_amount|floatformat:2 }}</div>
                    <small>Amount Due</small>
                </div>
                <hr style="border-color: rgba(255,255,255,0.3);">
                <div class="row text-sm">
                    <div class="col-6">
                        <strong>Invoice Total:</strong><br>
                        ₦{{ invoice.total_amount|floatformat:2 }}
                    </div>
                    <div class="col-6">
                        <strong>Amount Paid:</strong><br>
                        ₦{{ invoice.amount_paid|floatformat:2 }}
                    </div>
                </div>
            </div>

            <!-- Prescription Info -->
            <div class="prescription-info">
                <h6 class="font-weight-bold text-primary mb-2">
                    <i class="fas fa-prescription-bottle-alt"></i>
                    Prescription Details
                </h6>
                <div class="text-sm">
                    <p class="mb-1"><strong>Patient:</strong> {{ prescription.patient.get_full_name }}</p>
                    <p class="mb-1"><strong>Doctor:</strong> {{ prescription.doctor.get_full_name }}</p>
                    <p class="mb-1"><strong>Date:</strong> {{ prescription.date_prescribed|date:"M d, Y" }}</p>
                    <p class="mb-0"><strong>Items:</strong> {{ prescription.items.count }} medication(s)</p>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card payment-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-money-bill-wave"></i>
                        Process Payment
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" id="payment-form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.amount.id_for_label }}" class="font-weight-bold">
                                        <i class="fas fa-dollar-sign text-success"></i>
                                        Payment Amount
                                    </label>
                                    {{ form.amount }}
                                    {% if form.amount.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.amount.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Maximum: ₦{{ remaining_amount|floatformat:2 }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.payment_method.id_for_label }}" class="font-weight-bold">
                                        <i class="fas fa-credit-card text-info"></i>
                                        Payment Method
                                    </label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.payment_method.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.transaction_id.id_for_label }}" class="font-weight-bold">
                                <i class="fas fa-hashtag text-warning"></i>
                                Transaction ID <small class="text-muted">(Optional)</small>
                            </label>
                            {{ form.transaction_id }}
                            {% if form.transaction_id.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.transaction_id.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Reference number for electronic payments
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="font-weight-bold">
                                <i class="fas fa-sticky-note text-secondary"></i>
                                Payment Notes <small class="text-muted">(Optional)</small>
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-shield-alt"></i>
                                    Secure payment processing
                                </small>
                            </div>
                            <div>
                                <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" 
                                   class="btn btn-secondary mr-2">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-success btn-pay">
                                    <i class="fas fa-check-circle"></i>
                                    Process Payment
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Payment Methods Info -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="font-weight-bold text-primary mb-3">
                        <i class="fas fa-info-circle"></i>
                        Accepted Payment Methods
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-money-bill-alt fa-2x text-success mb-2"></i>
                            <div class="small">Cash</div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                            <div class="small">Credit Card</div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                            <div class="small">Mobile Payment</div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-university fa-2x text-warning mb-2"></i>
                            <div class="small">Bank Transfer</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('payment-form');
    const amountInput = document.getElementById('{{ form.amount.id_for_label }}');
    const paymentMethodSelect = document.getElementById('{{ form.payment_method.id_for_label }}');
    
    // Format amount input
    amountInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d.]/g, '');
        if (value.split('.').length > 2) {
            value = value.substring(0, value.lastIndexOf('.'));
        }
        this.value = value;
    });
    
    // Form validation
    form.addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value);
        const maxAmount = {{ remaining_amount }};
        
        if (isNaN(amount) || amount <= 0) {
            e.preventDefault();
            alert('Please enter a valid payment amount.');
            amountInput.focus();
            return;
        }
        
        if (amount > maxAmount) {
            e.preventDefault();
            alert(`Payment amount cannot exceed ₦${maxAmount.toFixed(2)}`);
            amountInput.focus();
            return;
        }
        
        // Confirm payment
        if (!confirm(`Confirm payment of ₦${amount.toFixed(2)} via ${paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text}?`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}

{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'pharmacy:create_prescription' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Create New Prescription
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
        </div>
        <div class="card-body">
            <form method="get" class="form-inline">
                {{ form.search|as_crispy_field }}
                {{ form.status|as_crispy_field }}
                {{ form.doctor|as_crispy_field }}
                {{ form.date_from|as_crispy_field }}
                {{ form.date_to|as_crispy_field }}
                <button type="submit" class="btn btn-primary ml-2">Search</button>
                <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary ml-2">Reset</a>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Prescription List</h6>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Doctor</th>
                            <th>Prescription Date</th>
                            <th>Status</th>
                            <th>Payment Status</th>
                            <th>Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prescription in page_obj %}
                        <tr>
                            <td>{{ prescription.patient.get_full_name }}</td>
                            <td>{{ prescription.doctor.get_full_name }}</td>
                            <td>{{ prescription.prescription_date }}</td>
                            <td>
                                <span class="badge badge-{{ prescription.status|default:'secondary' }}">
                                    {{ prescription.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% with payment_info=prescription.get_payment_status_display_info %}
                                <span class="badge badge-{{ payment_info.css_class }}">
                                    <i class="fas fa-{{ payment_info.icon }}"></i> {{ payment_info.message }}
                                </span>
                                {% endwith %}
                            </td>
                            <td>{{ prescription.get_prescription_type_display }}</td>
                            <td>
                                <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                {% if prescription.is_payment_verified %}
                                    <a href="{% url 'pharmacy:dispense_prescription' prescription.id %}" class="btn btn-success btn-sm">
                                        <i class="fas fa-pills"></i> Dispense
                                    </a>
                                {% else %}
                                    <button type="button" class="btn btn-success btn-sm" disabled title="Payment required">
                                        <i class="fas fa-lock"></i> Dispense
                                    </button>
                                {% endif %}
                                <a href="{% url 'pharmacy:print_prescription' prescription.id %}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-print"></i> Print
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <nav aria-label="Page navigation example">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.doctor %}&doctor={{ request.GET.doctor }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Previous</a></li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% else %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.doctor %}&doctor={{ request.GET.doctor }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.doctor %}&doctor={{ request.GET.doctor }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Next</a></li>
                    {% endif %}
                </ul>
            </nav>

            {% else %}
            <p>No prescriptions found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}